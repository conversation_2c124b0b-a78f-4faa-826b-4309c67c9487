const Service = require('egg').Service;

class SensitiveDataApprovalService extends Service {

  /**
   * 获取敏感数据审批列表
   * @param {Object} params - 查询参数
   * @returns {Object} 分页数据
   */
  async getDataReportApprovalList(params) {
    const {
      pageNum = 1,
      pageSize = 10,
      workerName,
      approvalStatus,
      startDate,
      endDate,
      superUserInfo
    } = params;

    const query = {};

    // 根据用户权限过滤数据
    // if (superUserInfo && superUserInfo._id) {
    //   query.superUserId = superUserInfo._id;
    // }

    // 按劳动者姓名搜索
    if (workerName) {
      query.dataName = new RegExp(workerName, 'i');
    }

    // 按审批状态搜索
    if (approvalStatus) {
      const statusMap = {
        'pending': 0,    // 待审批
        'processing': 0, // 审批中（也是0，通过process数组长度区分）
        'approved': 1,   // 已通过
        'rejected': 2    // 已拒绝（需要在model中添加此状态）
      };

      if (approvalStatus === 'processing') {
        // 审批中：状态为0且有审批记录
        query.status = 0;
        query['process.0'] = { $exists: true };
      } else if (approvalStatus === 'pending') {
        // 待审批：状态为0且无审批记录
        query.status = 0;
        query.process = { $size: 0 };
      } else {
        query.status = statusMap[approvalStatus];
      }
    }

    // 按时间范围搜索
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    }

    // 根据账号辖区进行筛选

    const list = await this.ctx.model.SensitiveDataApproval
      .find(query)
      .sort({ createdAt: -1 })
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .populate('superUserId', 'name')
      .lean();

    const total = await this.ctx.model.SensitiveDataApproval.countDocuments(query);

    // 格式化返回数据
    const formattedList = list.map(item => ({
      id: item._id,
      workerName: item.dataName,
      approvalStatus: this.getApprovalStatus(item),
      reportReason: this.getReportReason(item),
      currentApprovalNode: this.getCurrentApprovalNode(item),
      reportTime: item.createdAt,
      enterpriseName: item.superUserName || (item.superUserId && item.superUserId.name) || '未知企业',
      dataModel: item.dataModel
    }));

    return { list: formattedList, total };
  }

  /**
   * 获取审批流程详情
   * @param {Object} params - 查询参数
   * @returns {Object} 流程详情
   */
  async getApprovalFlowDetail(params) {
    const { id } = params;

    const approval = await this.ctx.model.SensitiveDataApproval
      .findById(id)
      .populate('process.approver', 'name')
      .populate('superUserId', 'name')
      .lean();

    if (!approval) {
      throw new Error('审批记录不存在');
    }

    // 定义审批步骤
    const approvalSteps = [
      { nodeName: '团场', description: '团场级审核' },
      { nodeName: '师市', description: '师市级审核' },
      { nodeName: '兵团', description: '兵团级审核' }
    ];

    // 计算当前步骤
    let currentStep = 0;
    if (approval.status === 1) {
      currentStep = 3; // 已完成
    } else if (approval.process.length > 0) {
      currentStep = approval.process.length;
    }

    // 格式化审批历史
    const approvalHistory = approval.process.map(item => ({
      nodeName: this.getLevelName(item.level),
      approverName: item.approver ? item.approver.name : '未知',
      approvalTime: item.time,
      approvalResult: item.result === 1 ? '通过' : '拒绝',
      approvalComment: item.comment || '无'
    }));

    return {
      currentStep,
      approvalSteps,
      approvalHistory
    };
  }

  /**
   * 提交审批结果
   * @param {Object} params - 审批参数
   * @returns {Object} 审批结果
   */
  async submitApprovalResult(params) {
    const { id, result, comment, superUserInfo } = params;

    const approval = await this.ctx.model.SensitiveDataApproval.findById(id);
    if (!approval) {
      throw new Error('审批记录不存在');
    }

    if (approval.status === 1) {
      throw new Error('该记录已审批完成，无法重复审批');
    }

    // 确定当前用户的审批级别
    const userLevel = this.getUserLevel(superUserInfo);

    // 检查是否有权限审批
    if (!this.canApprove(approval, userLevel)) {
      throw new Error('您没有权限审批此记录');
    }

    // 添加审批记录
    const approvalRecord = {
      approver: superUserInfo._id,
      time: new Date(),
      result: result === 'approve' ? 1 : 0,
      comment: comment,
      level: userLevel
    };

    approval.process.push(approvalRecord);

    // 更新审批状态和当前节点
    if (result === 'reject') {
      // 拒绝则直接结束流程
      approval.status = 2; // 需要在model中添加拒绝状态
    } else if (userLevel === 'province') {
      // 兵团级通过则完成审批
      approval.status = 1;
    } else {
      // 更新当前审批节点
      approval.currentNode = this.getNextNode(userLevel);
    }

    await approval.save();

    return { success: true, message: '审批提交成功' };
  }

  /**
   * 创建敏感数据审批记录
   * @param {Object} params - 创建参数
   * @returns {Object} 创建结果
   */
  async createApprovalRecord(params) {
    const {
      dataId,
      dataName,
      dataModel,
      reportReason,
      superUserInfo
    } = params;

    // 检查是否已存在待审批的记录
    const existingRecord = await this.ctx.model.SensitiveDataApproval.findOne({
      dataId,
      dataModel,
      status: { $in: [0] } // 审批中
    });

    if (existingRecord) {
      throw new Error('该数据已有待审批记录，请勿重复提交');
    }

    const approvalRecord = new this.ctx.model.SensitiveDataApproval({
      dataId,
      dataName,
      dataModel,
      reportReason,
      superUserId: superUserInfo._id,
      superUserName: superUserInfo.name,
      currentNode: '团场', // 默认从团场开始
      status: 0,
      process: []
    });

    await approvalRecord.save();
    return { success: true, data: approvalRecord };
  }

  /**
   * 删除审批记录
   * @param {Object} params - 删除参数
   * @returns {Object} 删除结果
   */
  async deleteApprovalRecord(params) {
    const { id, superUserInfo } = params;

    const approval = await this.ctx.model.SensitiveDataApproval.findById(id);
    if (!approval) {
      throw new Error('审批记录不存在');
    }

    // 只有创建者可以删除，且只能删除未开始审批的记录
    if (approval.superUserId !== superUserInfo._id) {
      throw new Error('您没有权限删除此记录');
    }

    if (approval.process.length > 0) {
      throw new Error('已开始审批的记录无法删除');
    }

    await this.ctx.model.SensitiveDataApproval.findByIdAndDelete(id);
    return { success: true, message: '删除成功' };
  }

  // 辅助方法
  getApprovalStatus(item) {
    if (item.status === 1) return 'approved';
    if (item.status === 2) return 'rejected';
    if (item.process.length > 0) return 'processing';
    return 'pending';
  }

  getReportReason(item) {
    return item.reportReason || '敏感数据上报审批';
  }

  getCurrentApprovalNode(item) {
    if (item.status === 1) return '已完成';
    if (item.status === 2) return '已拒绝';
    return item.currentNode || '团场';
  }

  getLevelName(level) {
    const levelMap = {
      'district': '团场',
      'city': '师市',
      'province': '兵团'
    };
    return levelMap[level] || level;
  }

  getUserLevel(superUserInfo) {
    // 根据用户信息确定级别，这里需要根据实际业务逻辑调整
    if (superUserInfo.level) {
      return superUserInfo.level;
    }
    // 默认返回district级别
    return 'district';
  }

  canApprove(approval, userLevel) {
    // 检查用户是否有权限审批当前记录
    const currentNode = approval.currentNode;
    const levelNodeMap = {
      'district': '团场',
      'city': '师市',
      'province': '兵团'
    };

    return levelNodeMap[userLevel] === currentNode;
  }

  getNextNode(currentLevel) {
    const nextNodeMap = {
      'district': '师市',
      'city': '兵团',
      'province': null
    };
    return nextNodeMap[currentLevel];
  }

  /**
   * 更新审批记录
   * @param {Object} params - 更新参数
   * @returns {Object} 更新结果
   */
  async updateApprovalRecord(params) {
    const { id, dataName, reportReason, superUserInfo } = params;

    const approval = await this.ctx.model.SensitiveDataApproval.findById(id);
    if (!approval) {
      throw new Error('审批记录不存在');
    }

    // 只有创建者可以更新，且只能更新未开始审批的记录
    if (approval.superUserId !== superUserInfo._id) {
      throw new Error('您没有权限更新此记录');
    }

    if (approval.process.length > 0) {
      throw new Error('已开始审批的记录无法更新');
    }

    // 更新字段
    if (dataName) approval.dataName = dataName;
    if (reportReason) approval.reportReason = reportReason;

    await approval.save();
    return { success: true, data: approval };
  }

  /**
   * 获取审批记录详情
   * @param {Object} params - 查询参数
   * @returns {Object} 记录详情
   */
  async getApprovalRecordDetail(params) {
    const { id } = params;

    const approval = await this.ctx.model.SensitiveDataApproval
      .findById(id)
      .populate('superUserId', 'name')
      .populate('process.approver', 'name')
      .lean();

    if (!approval) {
      throw new Error('审批记录不存在');
    }

    return {
      id: approval._id,
      dataId: approval.dataId,
      dataName: approval.dataName,
      dataModel: approval.dataModel,
      reportReason: approval.reportReason,
      status: approval.status,
      currentNode: approval.currentNode,
      superUserName: approval.superUserName,
      createdAt: approval.createdAt,
      updatedAt: approval.updatedAt,
      process: approval.process
    };
  }

  // 获取当前账号是否有上报权
  async getCanReport() {
    const { ctx } = this;
    const superUserInfo = ctx.session.superUserInfo;
    const superUser = await this.ctx.model.SuperUser.findOne({ _id: superUserInfo._id });
    return superUser.dataReportAuth;
  }

  // 获取 当前审批事项的审批流程
  async getApprovalFlow(params) {
    const { ctx } = this;
    const superUserInfo = ctx.session.superUserInfo;
    const { key } = params;
    const code = '100' // 当前账号类型 
    // key值枚举：
    // "worker_health_record_approval"
    // "enterprise_health_record_approval"

    const result = await ctx.curl(
      `${ctx.app.config.xjbtportal}/api/uaProcess/versions/current?itemCode=${key}&orgCategoryCode=${code}`,
      {
        method: 'get',
        dataType: 'json', // 返回的数据类型
      })
    if (result.status === 200 && result.data && result.data.data) {
      ctx.helper.renderSuccess(ctx, {
        data: result.data.data,
        message: '获取成功'
      });
    }
    // 接口示例
    // https://xjbtportal.jkqy.cn/api/uaProcess/versions/current?itemCode=worker_health_record_approval&orgCategoryCode=100
    // {
    //   "status": 200,
    //     "data": {
    //     "_id": "d1fb9ca1-17b3-4cdf-bb1c-4d395b9f6e75", // 流程id
    //       "orgCategoryCodes": [
    //         "120",
    //         "121",
    //         "100"
    //       ],
    //         "templateId": "96sG4jigw",
    //           "itemCode": "worker_health_record_approval",
    //             "steps": [ // 审批步骤
    //               {
    //                 "order": 1, //  顺序（无实际意义，可忽略
    //                 "levelCode": "3", // 审批级别 目前固定 1-4级，从第4级开始，可以按照这个排序，实际也是按照这个审批而不是order
    //                 "levelName": "团场/区县级"
    //               },
    //               {
    //                 "order": 2,
    //                 "levelCode": "2",
    //                 "levelName": "师市级"
    //               },
    //               {
    //                 "order": 3,
    //                 "levelCode": "1",
    //                 "levelName": "兵团/省级"
    //               },
    //               {
    //                 "order": 4,
    //                 "levelCode": "4",
    //                 "levelName": "连队/乡镇级"
    //               }
    //             ],
    //               "itemName": "“一人一档”敏感数据上报与审批", // 审批流程名称
    //                 "orgCategoryNames": [
    //                   "疾病预防控制中心",
    //                   "职业病防治院所",
    //                   "卫生行政管理部门"
    //                 ]
    //   },
    //   "message": ""
    // }

  }

}

module.exports = SensitiveDataApprovalService;
