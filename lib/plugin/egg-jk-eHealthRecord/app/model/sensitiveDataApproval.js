module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  // 上报时：以劳动者/企业所在地区上报，上报流程从最低一级开始，不管是谁上报
  // 提交时获取当前审批流程，在当前流程结束前不能变更，如果某级别审批被拒绝，修改审批状态，并重新开始新的审批流程（重新获取最新审批流程）
  // 审批时：按照审批流程顺序审批，只有机构类型和管辖区域相匹配的账号才能审批，拥有更高管辖区域的账号能查看但不能审批
  const SensitiveDataApproval = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    // 敏感数据id
    dataId: {
      type: String,
      refPath: 'dataModel',
    },

    // 数据名称 劳动者姓名/企业名称
    dataName: String,

    // 数据模型
    dataModel: {
      type: String,
      required: true,
      enum: ['Employees', 'Adminorg'],
    },

    // 当前审批状态
    status: {
      type: Number,
      enum: [0, 1, 2], // 0 待审批（此时可以修改上报理由）  1 审批中 2审批完成（已通过所有流程） 如果任意一级审批被拒绝，则重新开始一轮新的审批流程
      default: 0,
    },

    // 上报理由
    reportReason: {
      type: String,
    },

    // 审批流id
    flowId: {
      type: String,
    },

    // 审批路径
    steps: {
      type: Array,
    },

    // 当前审批节点 审批级别中文名称
    currentNode: {
      type: String,
    },

    // 审批过程
    process: [{
      _id: false,
      approver: { type: String, ref: 'SuperUser' }, // 审批人
      time: Date, // 审批时间
      result: { // 审批结果 是否通过
        type: Boolean,
      },
      comment: String, // 审批意见
      // 审批单位级别 省市区
      level: {
        type: String,
      },
    }],

    // 上报单位id
    superUserId: {
      type: String,
      ref: 'SuperUser',
    },
    // 上报单位名称
    superUserName: String,

  }, {
    // 上报时间 即 创建时间
    timestamps: true,
  });

  // 创建索引
  SensitiveDataApproval.index({ dataName: 1 });


  return mongoose.model('SensitiveDataApproval', SensitiveDataApproval, 'sensitiveDataApproval');
};
