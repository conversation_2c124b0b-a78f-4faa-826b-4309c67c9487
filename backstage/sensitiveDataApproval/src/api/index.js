import request from '@root/publicMethods/request';

// 获取劳动者列表
export function getEmployeeList(data) {
  return request({
    url: '/manage/eHealthRecord/getEmployeeList',
    method: 'post',
    data,
  });
}

// 获取行业分类
export function getIndustryCategory(params) {
  return request({
    url: '/api/adminorgGov/getIndustryCategory',
    method: 'get',
    params,
  });
}

// 获取危害因素
export function findHarmFactors(params) {
  return request({
    url: '/manage/eHealthRecord/findHarmFactors',
    method: 'get',
    params,
  });
}

// 获取地址信息
export function getDistrictList(params) {
  return request({
    url: '/api/adminorgGov/address/list',
    params,
    method: 'get',
  });
}

// 获取数据上报审批列表
export function getDataReportApprovalList(data) {
  return request({
    url: '/manage/sensitiveDataApproval/getDataReportApprovalList',
    method: 'post',
    data,
  });
}

// 获取审批流程详情
export function getApprovalFlowDetail(params) {
  return request({
    url: '/manage/sensitiveDataApproval/getApprovalFlowDetail',
    method: 'get',
    params,
  });
}

// 提交审批结果
export function submitApprovalResult(data) {
  return request({
    url: '/manage/sensitiveDataApproval/submitApprovalResult',
    method: 'post',
    data,
  });
}
